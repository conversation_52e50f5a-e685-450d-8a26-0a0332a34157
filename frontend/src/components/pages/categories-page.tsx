'use client'

import React, { useState, useMemo } from 'react'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Search, Grid, List, FolderTree, ChevronRight, Home, X, ArrowRight, Eye } from 'lucide-react'
import Image from 'next/image'
import { AnimatedBackground } from '@/components/ui/animated-background'
import { Category } from '@/types'
import { categoryService } from '@/lib/services/categories'
import { useQuery } from '@tanstack/react-query'

export function CategoriesPage() {
  const searchParams = useSearchParams()
  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState('name')
  const [selectedParent, setSelectedParent] = useState('')
  const [showActiveOnly, setShowActiveOnly] = useState(true)
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [useAdvancedSearch, setUseAdvancedSearch] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(12)

  // Fetch category tree for better hierarchy display
  const {
    data: categoryTree,
    isLoading: isTreeLoading,
    error: treeError
  } = useQuery({
    queryKey: ['categories', 'tree'],
    queryFn: () => categoryService.getCategoryTree(),
  })

  // Use advanced search/filter API when search query exists or filters are applied
  const shouldUseAdvancedAPI = searchQuery.trim() || selectedParent || !showActiveOnly || useAdvancedSearch

  // Flatten tree for easier filtering
  const flattenCategories = (categories: Category[]): Category[] => {
    const result: Category[] = []
    const flatten = (cats: Category[], level = 0) => {
      cats.forEach(cat => {
        result.push({ ...cat, level })
        if (cat.children && cat.children.length > 0) {
          flatten(cat.children, level + 1)
        }
      })
    }
    flatten(categories)
    return result
  }

  const flatCategories = useMemo(() => {
    return categoryTree ? flattenCategories(categoryTree) : []
  }, [categoryTree])

  const isLoading = isTreeLoading
  const error = treeError

  // Enhanced filtering with better search capabilities
  const filteredCategories = useMemo(() => {
    let catsToFilter = flatCategories

    // Active filter
    if (showActiveOnly) {
      catsToFilter = catsToFilter.filter(category => category.is_active !== false)
    }

    // Enhanced search filter - search in name, description, and slug
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim()
      catsToFilter = catsToFilter.filter(category => {
        const searchableText = [
          category.name,
          category.description || '',
          category.slug || '',
          // Also search in parent name for better hierarchy search
          category.parent?.name || ''
        ].join(' ').toLowerCase()

        return searchableText.includes(query)
      })
    }

    // Parent category filter
    if (selectedParent) {
      if (selectedParent === 'root') {
        catsToFilter = catsToFilter.filter(category => !category.parent_id)
      } else {
        catsToFilter = catsToFilter.filter(category =>
          category.parent_id === selectedParent
        )
      }
    }

    // Enhanced sorting
    catsToFilter.sort((a, b) => {
      let result = 0
      switch (sortBy) {
        case 'name':
          result = a.name.localeCompare(b.name)
          break
        case 'products':
          result = (b.product_count || 0) - (a.product_count || 0)
          break
        case 'level':
          result = (a.level || 0) - (b.level || 0)
          break
        default:
          result = 0
      }
      return sortOrder === 'desc' ? -result : result
    })

    return catsToFilter
  }, [flatCategories, searchQuery, selectedParent, sortBy, sortOrder, showActiveOnly])

  // Pagination logic
  const paginatedCategories = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    return filteredCategories.slice(startIndex, endIndex)
  }, [filteredCategories, currentPage, itemsPerPage])

  const totalPages = Math.ceil(filteredCategories.length / itemsPerPage)

  // Reset to first page when filters change
  const resetPagination = () => {
    setCurrentPage(1)
  }

  // Update pagination when search or filters change
  React.useEffect(() => {
    resetPagination()
  }, [searchQuery, selectedParent, sortBy, sortOrder, showActiveOnly])

  // Enhanced statistics for better UX and analytics
  const categoryStats = useMemo(() => {
    const total = flatCategories.length
    const active = flatCategories.filter(cat => cat.is_active !== false).length
    const inactive = total - active
    const rootCount = categoryTree?.length || 0
    const filtered = filteredCategories.length
    const totalProducts = flatCategories.reduce((sum, cat) => sum + (cat.product_count || 0), 0)

    // Calculate hierarchy depth
    const maxLevel = flatCategories.reduce((max, cat) => Math.max(max, cat.level || 0), 0)

    // Categories by level
    const categoriesByLevel = Array.from({ length: maxLevel + 1 }, (_, level) =>
      flatCategories.filter(cat => (cat.level || 0) === level).length
    )

    // Top categories by product count
    const topCategories = [...flatCategories]
      .sort((a, b) => (b.product_count || 0) - (a.product_count || 0))
      .slice(0, 5)

    // Categories with no products
    const emptyCategories = flatCategories.filter(cat => (cat.product_count || 0) === 0).length

    // Average products per category
    const avgProductsPerCategory = total > 0 ? Math.round(totalProducts / total) : 0

    return {
      total,
      active,
      inactive,
      rootCount,
      filtered,
      totalProducts,
      maxLevel,
      categoriesByLevel,
      topCategories,
      emptyCategories,
      avgProductsPerCategory
    }
  }, [flatCategories, categoryTree, filteredCategories])

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    const url = new URL(window.location.href)
    if (query) {
      url.searchParams.set('search', query)
    } else {
      url.searchParams.delete('search')
    }
    window.history.replaceState({}, '', url.toString())
  }

  if (error) {
    return (
      <div className="min-h-screen bg-black">
        <div className="container mx-auto px-4 py-8">
          <Card className="p-8 text-center bg-gray-900 border-gray-700">
            <h2 className="text-2xl font-bold text-white mb-4">
              Oops! Something went wrong
            </h2>
            <p className="text-gray-300 mb-6">
              We couldn't load the categories. Please try again later.
            </p>
            <Button onClick={() => window.location.reload()} variant="outline" className="bg-[#ff9000] text-white border-[#ff9000] hover:bg-[#ff9000]/90">
              Try Again
            </Button>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white relative overflow-hidden">
      {/* Enhanced Background Pattern */}
      <AnimatedBackground className="opacity-30" />

      {/* Main Content Area */}
      <div className="container mx-auto px-4 lg:px-6 xl:px-8 py-4 lg:py-8 relative z-10">
        <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
          {/* Mobile-Optimized Sidebar Filters */}
          <div className="lg:w-80 flex-shrink-0">
            <div className="lg:sticky lg:top-8">
              {/* Mobile Collapsible Filters */}
              <div className="lg:hidden mb-6">
                <Button
                  variant="outline"
                  className="w-full justify-between bg-gray-900/50 border-gray-700 text-white"
                  onClick={() => {
                    const sidebar = document.getElementById('mobile-sidebar')
                    sidebar?.classList.toggle('hidden')
                  }}
                >
                  <span>Filters & Categories</span>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>

              <div id="mobile-sidebar" className="hidden lg:block space-y-6">
                {/* Breadcrumb */}
                <nav className="flex items-center space-x-2 text-sm text-gray-400 mb-6">
                  <Link href="/" className="hover:text-white transition-colors">
                    <Home className="h-4 w-4" />
                  </Link>
                  <ChevronRight className="h-4 w-4" />
                  <span className="text-white">Categories</span>
                </nav>

                {/* Title */}
                <h1 className="text-3xl font-bold text-white mb-8">Categories</h1>

                {/* Sort By */}
                <div className="mb-6">
                  <div className="flex gap-2 mb-2">
                    <Select value={sortBy} onValueChange={setSortBy}>
                      <SelectTrigger className="flex-1 bg-gray-900/50 border-gray-700 text-white">
                        <SelectValue placeholder="Sort by" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-900 border-gray-700">
                        <SelectItem value="name">Name</SelectItem>
                        <SelectItem value="products">Product Count</SelectItem>
                        <SelectItem value="level">Hierarchy Level</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                      className="px-3 bg-gray-900/50 border-gray-700 text-white hover:bg-gray-800"
                    >
                      {sortOrder === 'asc' ? '↑' : '↓'}
                    </Button>
                  </div>

                  {/* Active Only Filter */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-400">Show active only</span>
                    <Button
                      variant={showActiveOnly ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setShowActiveOnly(!showActiveOnly)}
                      className={showActiveOnly ? 'bg-[#ff9000] text-white' : 'bg-gray-900/50 border-gray-700 text-gray-400'}
                    >
                      {showActiveOnly ? 'ON' : 'OFF'}
                    </Button>
                  </div>
                </div>

                {/* Categories Filter */}
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-4">Filter by Parent</h3>
                    <div className="space-y-2">
                      <Button
                        variant={selectedParent === '' ? 'default' : 'ghost'}
                        className={`w-full justify-start text-left ${
                          selectedParent === ''
                            ? 'bg-[#ff9000] text-white'
                            : 'text-gray-400 hover:text-white hover:bg-gray-800'
                        }`}
                        onClick={() => setSelectedParent('')}
                      >
                        All Categories
                      </Button>
                      <Button
                        variant={selectedParent === 'root' ? 'default' : 'ghost'}
                        className={`w-full justify-start text-left ${
                          selectedParent === 'root'
                            ? 'bg-[#ff9000] text-white'
                            : 'text-gray-400 hover:text-white hover:bg-gray-800'
                        }`}
                        onClick={() => setSelectedParent('root')}
                      >
                        Root Categories Only
                      </Button>
                      {categoryTree?.map((category) => (
                        <Button
                          key={category.id}
                          variant={selectedParent === category.id.toString() ? 'default' : 'ghost'}
                          className={`w-full justify-start text-left ${
                            selectedParent === category.id.toString()
                              ? 'bg-[#ff9000] text-white'
                              : 'text-gray-400 hover:text-white hover:bg-gray-800'
                          }`}
                          onClick={() => setSelectedParent(category.id.toString())}
                        >
                          {category.name}
                          {category.children && category.children.length > 0 && (
                            <Badge variant="secondary" className="ml-auto bg-gray-700 text-gray-300">
                              {category.children.length}
                            </Badge>
                          )}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Enhanced Category Analytics */}
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-4">Analytics</h3>
                    <div className="space-y-3">
                    {/* Basic Stats */}
                    <div className="grid grid-cols-2 gap-2">
                      <div className="p-3 bg-gray-900/30 rounded-lg text-center">
                        <div className="text-lg font-bold text-[#ff9000]">{categoryStats.total}</div>
                        <div className="text-xs text-gray-400">Total</div>
                      </div>
                      <div className="p-3 bg-gray-900/30 rounded-lg text-center">
                        <div className="text-lg font-bold text-green-500">{categoryStats.active}</div>
                        <div className="text-xs text-gray-400">Active</div>
                      </div>
                    </div>

                    {/* Hierarchy Stats */}
                    <div className="p-3 bg-gray-900/30 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-300">Hierarchy Depth</span>
                        <Badge variant="secondary" className="bg-blue-500/20 text-blue-500">
                          {categoryStats.maxLevel + 1} levels
                        </Badge>
                      </div>
                      <div className="space-y-1">
                        {categoryStats.categoriesByLevel.map((count, level) => (
                          <div key={level} className="flex items-center justify-between text-xs">
                            <span className="text-gray-400">Level {level}</span>
                            <span className="text-gray-300">{count}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Product Distribution */}
                    <div className="p-3 bg-gray-900/30 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-300">Products</span>
                        <Badge variant="secondary" className="bg-green-500/20 text-green-500">
                          {categoryStats.totalProducts}
                        </Badge>
                      </div>
                      <div className="space-y-1 text-xs">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Avg per category</span>
                          <span className="text-gray-300">{categoryStats.avgProductsPerCategory}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Empty categories</span>
                          <span className="text-gray-300">{categoryStats.emptyCategories}</span>
                        </div>
                      </div>
                    </div>

                    {/* Current Filter */}
                    <div className="p-3 bg-gray-900/30 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Eye className="h-4 w-4 text-[#ff9000]" />
                          <span className="text-sm text-gray-300">Showing</span>
                        </div>
                        <Badge variant="secondary" className="bg-[#ff9000]/20 text-[#ff9000]">
                          {categoryStats.filtered}
                        </Badge>
                      </div>
                      {(searchQuery || selectedParent || !showActiveOnly) && (
                        <div className="mt-2 text-xs text-gray-400">
                          {searchQuery && <div>Search: "{searchQuery}"</div>}
                          {selectedParent && <div>Parent filter active</div>}
                          {!showActiveOnly && <div>Including inactive</div>}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Top Categories */}
                {categoryStats.topCategories.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-4">Top Categories</h3>
                    <div className="space-y-2">
                      {categoryStats.topCategories.map((category, index) => (
                        <div key={category.id} className="flex items-center gap-3 p-2 bg-gray-900/30 rounded-lg">
                          <div className="flex items-center justify-center w-6 h-6 bg-[#ff9000]/20 text-[#ff9000] text-xs font-bold rounded">
                            {index + 1}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="text-sm text-white truncate">{category.name}</div>
                            <div className="text-xs text-gray-400">{category.product_count || 0} products</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 min-w-0">
            {/* Enhanced Search and View Controls */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6 lg:mb-8">
              {/* Enhanced Search with suggestions */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search categories by name, description, or slug..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10 pr-10 bg-gray-900/50 border-gray-700 text-white placeholder-gray-400 focus:border-[#ff9000]"
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSearch('')}
                    className="absolute right-2 top-1/2 -translate-y-1/2 h-7 w-7 p-0 text-gray-400 hover:text-white hover:bg-gray-800 rounded-md"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}

                {/* Search Results Summary */}
                {searchQuery && (
                  <div className="absolute top-full left-0 right-0 mt-1 p-2 bg-gray-900/90 border border-gray-700 rounded-md text-xs text-gray-400">
                    Found {filteredCategories.length} categories matching "{searchQuery}"
                    {filteredCategories.length === 0 && (
                      <div className="mt-1 text-[#ff9000]">
                        Try searching for: {flatCategories.slice(0, 3).map(cat => cat.name).join(', ')}
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* View Mode Toggle and Items Per Page */}
              <div className="flex items-center gap-3">
                {/* Items per page selector */}
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-400">Show:</span>
                  <Select value={itemsPerPage.toString()} onValueChange={(value) => {
                    setItemsPerPage(parseInt(value))
                    setCurrentPage(1)
                  }}>
                    <SelectTrigger className="w-20 h-8 bg-gray-900/50 border-gray-700 text-white text-sm">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-900 border-gray-700">
                      <SelectItem value="6">6</SelectItem>
                      <SelectItem value="12">12</SelectItem>
                      <SelectItem value="24">24</SelectItem>
                      <SelectItem value="48">48</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* View Mode Toggle */}
                <div className="flex items-center bg-white/[0.06] backdrop-blur-md rounded-lg border border-white/10 p-0.5">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className={`h-8 px-3 text-sm transition-all duration-200 ${
                      viewMode === 'grid'
                        ? 'bg-[#ff9000] text-white shadow-sm'
                        : 'text-gray-400 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    <Grid className="h-4 w-4 mr-2" />
                    Grid
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className={`h-8 px-3 text-sm transition-all duration-200 ${
                      viewMode === 'list'
                        ? 'bg-[#ff9000] text-white shadow-sm'
                        : 'text-gray-400 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    <List className="h-4 w-4 mr-2" />
                    List
                  </Button>
                </div>
              </div>
            </div>

            {/* Enhanced Content with Better Loading States */}
            {isLoading ? (
              <div className="space-y-6">
                {/* Loading Header */}
                <div className="flex items-center justify-between">
                  <div className="h-8 w-48 bg-gray-800 animate-pulse rounded" />
                  <div className="h-8 w-32 bg-gray-800 animate-pulse rounded" />
                </div>

                {/* Loading Content */}
                <div className={viewMode === 'grid'
                  ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
                  : "space-y-4"
                }>
                  {[...Array(6)].map((_, i) => (
                    <Card key={i} className="bg-gray-900/50 border-gray-700 overflow-hidden">
                      <CardContent className="p-0">
                        {viewMode === 'grid' ? (
                          <>
                            <div className="w-full h-48 bg-gray-800 animate-pulse" />
                            <div className="p-4">
                              <div className="h-5 w-3/4 mb-2 bg-gray-800 animate-pulse rounded" />
                              <div className="h-4 w-full mb-3 bg-gray-800 animate-pulse rounded" />
                              <div className="h-4 w-2/3 mb-3 bg-gray-800 animate-pulse rounded" />
                              <div className="flex gap-2">
                                <div className="h-6 w-16 bg-gray-800 animate-pulse rounded" />
                                <div className="h-6 w-12 bg-gray-800 animate-pulse rounded" />
                              </div>
                            </div>
                          </>
                        ) : (
                          <div className="flex gap-4 p-4">
                            <div className="w-24 h-24 bg-gray-800 animate-pulse rounded-lg flex-shrink-0" />
                            <div className="flex-1">
                              <div className="h-6 w-3/4 mb-2 bg-gray-800 animate-pulse rounded" />
                              <div className="h-4 w-full mb-2 bg-gray-800 animate-pulse rounded" />
                              <div className="h-4 w-2/3 mb-3 bg-gray-800 animate-pulse rounded" />
                              <div className="flex gap-2">
                                <div className="h-6 w-20 bg-gray-800 animate-pulse rounded" />
                                <div className="h-6 w-16 bg-gray-800 animate-pulse rounded" />
                              </div>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ) : filteredCategories.length > 0 ? (
              <>
                {viewMode === 'grid' ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    {paginatedCategories.map((category) => (
                    <Link key={category.id} href={`/categories/${category.slug || category.id}`}>
                      <Card className="bg-gray-900/50 border-gray-700 hover:border-[#ff9000]/50 transition-all duration-300 group overflow-hidden">
                        <CardContent className="p-0">
                          {/* Category Image */}
                          <div className="relative w-full h-48 bg-gradient-to-br from-[#ff9000]/20 to-[#ff9000]/10 flex items-center justify-center">
                            {category.image ? (
                              <Image
                                src={category.image}
                                alt={category.name}
                                width={120}
                                height={120}
                                className="object-cover rounded-lg group-hover:scale-110 transition-transform duration-500"
                              />
                            ) : (
                              <FolderTree className="w-16 h-16 text-[#ff9000]" />
                            )}
                            {/* Level indicator */}
                            {(category.level || 0) > 0 && (
                              <div className="absolute top-2 left-2 bg-[#ff9000]/80 text-white text-xs px-2 py-1 rounded">
                                Level {category.level}
                              </div>
                            )}
                          </div>

                          {/* Category Info */}
                          <div className="p-4">
                            <h3 className="text-lg font-semibold text-white mb-2 group-hover:text-[#ff9000] transition-colors line-clamp-1">
                              {category.name}
                            </h3>
                            {category.description && (
                              <p className="text-gray-400 mb-3 text-sm line-clamp-2">
                                {category.description}
                              </p>
                            )}
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <Badge variant="secondary" className="bg-gray-800 text-gray-300 text-xs">
                                  {category.product_count || 0} items
                                </Badge>
                                {category.children && category.children.length > 0 && (
                                  <Badge variant="secondary" className="bg-[#ff9000]/20 text-[#ff9000] text-xs">
                                    {category.children.length} sub
                                  </Badge>
                                )}
                              </div>
                              <ArrowRight className="h-4 w-4 text-[#ff9000] group-hover:translate-x-1 transition-transform duration-300" />
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {paginatedCategories.map((category) => (
                    <Link key={category.id} href={`/categories/${category.slug || category.id}`}>
                      <Card className="bg-gray-900/50 border-gray-700 hover:border-[#ff9000]/50 transition-all duration-300 group">
                        <CardContent className="p-0">
                          <div className="flex gap-4 p-4">
                            {/* Category Image/Icon */}
                            <div className="relative w-24 h-24 bg-gradient-to-br from-[#ff9000]/20 to-[#ff9000]/10 flex items-center justify-center flex-shrink-0 rounded-lg">
                              {category.image ? (
                                <Image
                                  src={category.image}
                                  alt={category.name}
                                  width={60}
                                  height={60}
                                  className="object-cover rounded-lg group-hover:scale-110 transition-transform duration-500"
                                />
                              ) : (
                                <FolderTree className="w-8 h-8 text-[#ff9000]" />
                              )}
                            </div>

                            {/* Category Info */}
                            <div className="flex-1 flex flex-col justify-between">
                              <div>
                                <div className="flex items-center gap-2 mb-2">
                                  {/* Hierarchy indicator */}
                                  {(category.level || 0) > 0 && (
                                    <div className="flex items-center text-xs text-gray-500">
                                      {'└─'.repeat(category.level || 0)}
                                    </div>
                                  )}
                                  <h3 className="text-lg font-semibold text-white group-hover:text-[#ff9000] transition-colors">
                                    {category.name}
                                  </h3>
                                  {(category.level || 0) > 0 && (
                                    <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                                      L{category.level}
                                    </Badge>
                                  )}
                                </div>
                                {category.description && (
                                  <p className="text-gray-400 mb-3 line-clamp-2 text-sm">
                                    {category.description}
                                  </p>
                                )}
                                <div className="flex items-center gap-3">
                                  <Badge variant="secondary" className="bg-gray-800 text-gray-300">
                                    {category.product_count || 0} products
                                  </Badge>
                                  {category.children && category.children.length > 0 && (
                                    <Badge variant="secondary" className="bg-[#ff9000]/20 text-[#ff9000]">
                                      {category.children.length} subcategories
                                    </Badge>
                                  )}
                                  {!category.is_active && (
                                    <Badge variant="destructive" className="text-xs">
                                      Inactive
                                    </Badge>
                                  )}
                                </div>
                              </div>

                              <div className="flex items-center justify-end mt-3">
                                <div className="flex items-center text-[#ff9000] group-hover:text-[#ff9000]/80">
                                  <span className="text-sm font-medium mr-2">Browse</span>
                                  <ArrowRight className="h-4 w-4 group-hover:translate-x-2 transition-transform duration-300" />
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              )}

              {/* Pagination Controls */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-700">
                  <div className="text-sm text-gray-400">
                    Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredCategories.length)} of {filteredCategories.length} categories
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="bg-gray-900/50 border-gray-700 text-white hover:bg-gray-800 disabled:opacity-50"
                    >
                      Previous
                    </Button>

                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const pageNum = i + 1
                        const isCurrentPage = pageNum === currentPage
                        return (
                          <Button
                            key={pageNum}
                            variant={isCurrentPage ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setCurrentPage(pageNum)}
                            className={isCurrentPage
                              ? 'bg-[#ff9000] text-white'
                              : 'bg-gray-900/50 border-gray-700 text-white hover:bg-gray-800'
                            }
                          >
                            {pageNum}
                          </Button>
                        )
                      })}

                      {totalPages > 5 && (
                        <>
                          <span className="text-gray-400 px-2">...</span>
                          <Button
                            variant={currentPage === totalPages ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setCurrentPage(totalPages)}
                            className={currentPage === totalPages
                              ? 'bg-[#ff9000] text-white'
                              : 'bg-gray-900/50 border-gray-700 text-white hover:bg-gray-800'
                            }
                          >
                            {totalPages}
                          </Button>
                        </>
                      )}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="bg-gray-900/50 border-gray-700 text-white hover:bg-gray-800 disabled:opacity-50"
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
              </>
            ) : (
              <div className="text-center py-16">
                <div className="max-w-md mx-auto">
                  <div className="relative mb-8">
                    <div className="absolute inset-0 bg-gradient-to-r from-[#ff9000]/20 to-transparent rounded-full blur-3xl" />
                    <FolderTree className="h-20 w-20 text-gray-600 mx-auto relative" />
                  </div>

                  <h3 className="text-2xl font-semibold text-white mb-3">
                    {searchQuery ? 'No matching categories' : 'No categories found'}
                  </h3>

                  <p className="text-gray-400 mb-8 leading-relaxed">
                    {searchQuery
                      ? `We couldn't find any categories matching "${searchQuery}". Try adjusting your search terms or browse all categories.`
                      : selectedParent
                        ? 'This category doesn\'t have any subcategories yet.'
                        : 'No categories are available at the moment.'
                    }
                  </p>

                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    {searchQuery && (
                      <Button
                        onClick={() => handleSearch('')}
                        variant="outline"
                        className="bg-[#ff9000] text-white border-[#ff9000] hover:bg-[#ff9000]/90"
                      >
                        Clear Search
                      </Button>
                    )}

                    {selectedParent && (
                      <Button
                        onClick={() => setSelectedParent('')}
                        variant="outline"
                        className="border-gray-600 text-gray-300 hover:bg-gray-800 hover:text-white"
                      >
                        Show All Categories
                      </Button>
                    )}

                    {!showActiveOnly && (
                      <Button
                        onClick={() => setShowActiveOnly(true)}
                        variant="outline"
                        className="border-gray-600 text-gray-300 hover:bg-gray-800 hover:text-white"
                      >
                        Show Active Only
                      </Button>
                    )}
                  </div>

                  {/* Suggestions */}
                  {searchQuery && flatCategories.length > 0 && (
                    <div className="mt-8 p-4 bg-gray-900/30 rounded-lg">
                      <p className="text-sm text-gray-400 mb-3">Popular categories:</p>
                      <div className="flex flex-wrap gap-2 justify-center">
                        {flatCategories.slice(0, 5).map((cat) => (
                          <Button
                            key={cat.id}
                            variant="ghost"
                            size="sm"
                            onClick={() => handleSearch(cat.name)}
                            className="text-xs bg-gray-800/50 text-gray-300 hover:bg-[#ff9000]/20 hover:text-[#ff9000]"
                          >
                            {cat.name}
                          </Button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

    </div>
  );




