'use client'

import { useState, useMemo } from 'react'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Search, Grid, List, FolderTree, ChevronRight, Home, X, ArrowRight } from 'lucide-react'
import Image from 'next/image'
import { ProductCard } from '@/components/products/product-card'
import { AnimatedBackground } from '@/components/ui/animated-background'
import { Category } from '@/types'
import { categoryService } from '@/lib/services/categories'
import { useQuery } from '@tanstack/react-query'

// Mock category as product for ProductCard
const categoryToProduct = (category: Category) => ({
  id: category.id,
  name: category.name,
  description: category.description || '',
  short_description: category.description || '',
  sku: `CAT-${category.id}`,
  slug: category.slug || category.id.toString(),
  main_image: category.image || '/placeholder-category.svg',
  images: category.image ? [{
    id: `${category.id}-img`,
    url: category.image,
    alt_text: category.name,
    position: 1
  }] : [],
  current_price: 0, // Categories don't have prices
  original_price: 0,
  price: 0,
  has_discount: false,
  is_on_sale: false,
  discount_percentage: 0,
  sale_discount_percentage: 0,
  stock: category.product_count || 0,
  stock_status: 'in_stock' as const,
  is_low_stock: false,
  rating_average: 0,
  rating_count: 0,
  category: undefined,
  category_id: undefined,
  tags: [],
  attributes: [],
  variants: [],
  weight: 0,
  dimensions: { length: 0, width: 0, height: 0 },
  featured: false,
  status: 'active' as const,
  visibility: 'visible' as const,
  keywords: '',
  meta_title: category.name,
  meta_description: category.description || '',
  meta_keywords: '',
  sale_start_date: undefined,
  sale_end_date: undefined,
  min_quantity: 1,
  max_quantity: null,
  is_digital: false,
  download_url: null,
  download_limit: null,
  low_stock_threshold: 10,
  track_quantity: true,
  allow_backorder: false,
  requires_shipping: false,
  shipping_class: '',
  tax_class: '',
  related_products: [],
  cross_sell_products: [],
  up_sell_products: [],
  country_of_origin: '',
  product_type: 'simple' as const,
  is_available: true,
  has_variants: false,
  created_at: category.created_at || new Date().toISOString(),
  updated_at: category.updated_at || new Date().toISOString(),
})

export function CategoriesPage() {
  const searchParams = useSearchParams()
  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState('name')
  const [selectedParent, setSelectedParent] = useState('')
  const [priceRange, setPriceRange] = useState('')

  // Fetch all categories
  const {
    data: categories,
    isLoading,
    error
  } = useQuery({
    queryKey: ['categories'],
    queryFn: () => categoryService.getCategories(),
  })

  // Group categories by parent for hierarchy
  const categoriesHierarchy = useMemo(() => {
    if (!categories) return []

    const rootCats = categories.filter(cat => !cat.parent_id)
    return rootCats.map(root => ({
      ...root,
      children: categories.filter(cat => cat.parent_id === root.id)
    }))
  }, [categories])

  // Filter categories based on search query and filters
  const filteredCategories = useMemo(() => {
    let catsToFilter = categoriesHierarchy

    // Search filter
    if (searchQuery.trim()) {
      catsToFilter = catsToFilter.filter(category =>
        category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        category.description?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Parent category filter
    if (selectedParent) {
      catsToFilter = catsToFilter.filter(category =>
        category.id.toString() === selectedParent
      )
    }

    // Sort categories
    catsToFilter.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'products':
          return (b.product_count || 0) - (a.product_count || 0)
        default:
          return 0
      }
    })

    return catsToFilter
  }, [categoriesHierarchy, searchQuery, selectedParent, sortBy])

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    const url = new URL(window.location.href)
    if (query) {
      url.searchParams.set('search', query)
    } else {
      url.searchParams.delete('search')
    }
    window.history.replaceState({}, '', url.toString())
  }

  if (error) {
    return (
      <div className="min-h-screen bg-black">
        <div className="container mx-auto px-4 py-8">
          <Card className="p-8 text-center bg-gray-900 border-gray-700">
            <h2 className="text-2xl font-bold text-white mb-4">
              Oops! Something went wrong
            </h2>
            <p className="text-gray-300 mb-6">
              We couldn't load the categories. Please try again later.
            </p>
            <Button onClick={() => window.location.reload()} variant="outline" className="bg-[#ff9000] text-white border-[#ff9000] hover:bg-[#ff9000]/90">
              Try Again
            </Button>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white relative overflow-hidden">
      {/* Enhanced Background Pattern */}
      <AnimatedBackground className="opacity-30" />

      {/* Main Content Area */}
      <div className="container mx-auto px-4 lg:px-6 xl:px-8 py-8 relative z-10">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Filters */}
          <div className="lg:w-80 flex-shrink-0">
            <div className="sticky top-8">
              {/* Breadcrumb */}
              <nav className="flex items-center space-x-2 text-sm text-gray-400 mb-6">
                <Link href="/" className="hover:text-white transition-colors">
                  <Home className="h-4 w-4" />
                </Link>
                <ChevronRight className="h-4 w-4" />
                <span className="text-white">Categories</span>
              </nav>

              {/* Title */}
              <h1 className="text-3xl font-bold text-white mb-8">Categories</h1>

              {/* Sort By */}
              <div className="mb-6">
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-full bg-gray-900/50 border-gray-700 text-white">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900 border-gray-700">
                    <SelectItem value="name">Name</SelectItem>
                    <SelectItem value="products">Product Count</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Categories Filter */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Categories</h3>
                  <div className="space-y-2">
                    <Button
                      variant={selectedParent === '' ? 'default' : 'ghost'}
                      className={`w-full justify-start text-left ${
                        selectedParent === ''
                          ? 'bg-[#ff9000] text-white'
                          : 'text-gray-400 hover:text-white hover:bg-gray-800'
                      }`}
                      onClick={() => setSelectedParent('')}
                    >
                      All Categories
                    </Button>
                    {categoriesHierarchy.map((category) => (
                      <Button
                        key={category.id}
                        variant={selectedParent === category.id.toString() ? 'default' : 'ghost'}
                        className={`w-full justify-start text-left ${
                          selectedParent === category.id.toString()
                            ? 'bg-[#ff9000] text-white'
                            : 'text-gray-400 hover:text-white hover:bg-gray-800'
                        }`}
                        onClick={() => setSelectedParent(category.id.toString())}
                      >
                        {category.name}
                        {category.children && category.children.length > 0 && (
                          <Badge variant="secondary" className="ml-auto bg-gray-700 text-gray-300">
                            {category.children.length}
                          </Badge>
                        )}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Price Range Filter (placeholder) */}
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Price</h3>
                  <div className="space-y-2">
                    <Button
                      variant={priceRange === '' ? 'default' : 'ghost'}
                      className={`w-full justify-start text-left ${
                        priceRange === ''
                          ? 'bg-[#ff9000] text-white'
                          : 'text-gray-400 hover:text-white hover:bg-gray-800'
                      }`}
                      onClick={() => setPriceRange('')}
                    >
                      All Prices
                    </Button>
                    <Button
                      variant={priceRange === '0-50' ? 'default' : 'ghost'}
                      className={`w-full justify-start text-left ${
                        priceRange === '0-50'
                          ? 'bg-[#ff9000] text-white'
                          : 'text-gray-400 hover:text-white hover:bg-gray-800'
                      }`}
                      onClick={() => setPriceRange('0-50')}
                    >
                      $0 - $50
                    </Button>
                    <Button
                      variant={priceRange === '50-100' ? 'default' : 'ghost'}
                      className={`w-full justify-start text-left ${
                        priceRange === '50-100'
                          ? 'bg-[#ff9000] text-white'
                          : 'text-gray-400 hover:text-white hover:bg-gray-800'
                      }`}
                      onClick={() => setPriceRange('50-100')}
                    >
                      $50 - $100
                    </Button>
                    <Button
                      variant={priceRange === '100-200' ? 'default' : 'ghost'}
                      className={`w-full justify-start text-left ${
                        priceRange === '100-200'
                          ? 'bg-[#ff9000] text-white'
                          : 'text-gray-400 hover:text-white hover:bg-gray-800'
                      }`}
                      onClick={() => setPriceRange('100-200')}
                    >
                      $100 - $200
                    </Button>
                    <Button
                      variant={priceRange === '200+' ? 'default' : 'ghost'}
                      className={`w-full justify-start text-left ${
                        priceRange === '200+'
                          ? 'bg-[#ff9000] text-white'
                          : 'text-gray-400 hover:text-white hover:bg-gray-800'
                      }`}
                      onClick={() => setPriceRange('200+')}
                    >
                      Over $200
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 min-w-0">
            {/* Search and View Controls */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search categories..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10 bg-gray-900/50 border-gray-700 text-white placeholder-gray-400 focus:border-[#ff9000]"
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSearch('')}
                    className="absolute right-2 top-1/2 -translate-y-1/2 h-7 w-7 p-0 text-gray-400 hover:text-white hover:bg-gray-800 rounded-md"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {/* View Mode Toggle */}
              <div className="flex items-center bg-white/[0.06] backdrop-blur-md rounded-lg border border-white/10 p-0.5">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className={`h-8 px-3 text-sm transition-all duration-200 ${
                    viewMode === 'grid'
                      ? 'bg-[#ff9000] text-white shadow-sm'
                      : 'text-gray-400 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <Grid className="h-4 w-4 mr-2" />
                  Grid
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className={`h-8 px-3 text-sm transition-all duration-200 ${
                    viewMode === 'list'
                      ? 'bg-[#ff9000] text-white shadow-sm'
                      : 'text-gray-400 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <List className="h-4 w-4 mr-2" />
                  List
                </Button>
              </div>
            </div>

            {/* Content */}
            {isLoading ? (
              <div className={viewMode === 'grid'
                ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
                : "space-y-4"
              }>
                {[...Array(6)].map((_, i) => (
                  <Card key={i} className="bg-gray-900/50 border-gray-700 overflow-hidden">
                    <CardContent className="p-0">
                      <div className="w-full h-48 bg-gray-800 animate-pulse" />
                      <div className="p-6">
                        <div className="h-6 w-3/4 mb-3 bg-gray-800 animate-pulse rounded" />
                        <div className="h-4 w-1/2 mb-4 bg-gray-800 animate-pulse rounded" />
                        <div className="h-8 w-1/3 bg-gray-800 animate-pulse rounded" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : filteredCategories.length > 0 ? (
              viewMode === 'grid' ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredCategories.map((category) => (
                    <Link key={category.id} href={`/categories/${category.slug || category.id}`}>
                      <ProductCard
                        product={categoryToProduct(category)}
                        showQuickView={false}
                        showWishlist={false}
                      />
                    </Link>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredCategories.map((category) => (
                    <Link key={category.id} href={`/categories/${category.slug || category.id}`}>
                      <Card className="bg-gray-900/50 border-gray-700 hover:border-[#ff9000]/50 transition-all duration-300 group">
                        <CardContent className="p-0">
                          <div className="flex gap-4 p-4">
                            {/* Category Image/Icon */}
                            <div className="relative w-32 h-32 bg-gradient-to-br from-[#ff9000]/20 to-[#ff9000]/10 flex items-center justify-center flex-shrink-0 rounded-lg">
                              {category.image ? (
                                <Image
                                  src={category.image}
                                  alt={category.name}
                                  width={80}
                                  height={80}
                                  className="object-cover rounded-lg group-hover:scale-110 transition-transform duration-500"
                                />
                              ) : (
                                <FolderTree className="w-10 h-10 text-[#ff9000]" />
                              )}
                            </div>

                            {/* Category Info */}
                            <div className="flex-1 flex flex-col justify-between">
                              <div>
                                <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-[#ff9000] transition-colors">
                                  {category.name}
                                </h3>
                                {category.description && (
                                  <p className="text-gray-400 mb-4 line-clamp-2">
                                    {category.description}
                                  </p>
                                )}
                                <div className="flex items-center gap-4">
                                  <Badge variant="secondary" className="bg-gray-800 text-gray-300">
                                    {category.product_count || 0} products
                                  </Badge>
                                  {category.children && category.children.length > 0 && (
                                    <Badge variant="secondary" className="bg-gray-800 text-gray-300">
                                      {category.children.length} subcategories
                                    </Badge>
                                  )}
                                </div>
                              </div>

                              <div className="flex items-center justify-end mt-4">
                                <div className="flex items-center text-[#ff9000] group-hover:text-[#ff9000]/80">
                                  <span className="text-sm font-medium mr-2">Browse</span>
                                  <ArrowRight className="h-5 w-5 group-hover:translate-x-2 transition-transform duration-300" />
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              )
            ) : (
              <div className="text-center py-12">
                <FolderTree className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">No categories found</h3>
                <p className="text-gray-400 mb-6">
                  {searchQuery ? `No categories match "${searchQuery}"` : 'No categories available'}
                </p>
                {searchQuery && (
                  <Button
                    onClick={() => handleSearch('')}
                    variant="outline"
                    className="bg-[#ff9000] text-white border-[#ff9000] hover:bg-[#ff9000]/90"
                  >
                    Clear Search
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}



